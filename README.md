# CompleteView VMS v6 Database Anonymization

## OVERVIEW

This collection contains 12 standalone SQL modules for anonymizing CompleteView VMS database backups. Each module targets specific data types and can be executed independently in SQL Server Management Studio.

## EXECUTION ORDER (Execute in this exact sequence)

### STEP 1: Setup and Validation (REQUIRED FIRST)

- **File:** `01_Setup_and_Validation.sql`
- **Purpose:** Validates database state, checks for recent backups, creates logging tables

### STEP 2: Camera Data Anonymization

- **File:** `02_Camera_Data_Anonymization.sql`
- **Purpose:** Anonymizes camera names, IPs, GPS coordinates, ONVIF URLs

### STEP 3: Server Data Anonymization

- **File:** `03_Server_Data_Anonymization.sql`
- **Purpose:** Anonymizes server hostnames, IP addresses, DNS entries

### STEP 4: User Account Anonymization

- **File:** `04_User_Account_Anonymization.sql`
- **Purpose:** Anonymizes usernames, passwords, personal information

### STEP 5: Network Device Anonymization

- **File:** `05_Network_Device_Anonymization.sql`
- **Purpose:** Anonymizes video devices, alarm devices, audio devices, NVR settings

### STEP 6: Integration Credentials Anonymization

- **File:** `06_Integration_Credentials_Anonymization.sql`
- **Purpose:** Anonymizes API keys, certificates, authentication tokens

### STEP 7: URL and File Path Anonymization

- **File:** `07_URL_and_FilePath_Anonymization.sql`
- **Purpose:** Anonymizes file paths, UNC paths, volume settings, stream profiles

### STEP 8: Email and Domain Anonymization

- **File:** `08_Email_and_Domain_Anonymization.sql`
- **Purpose:** Anonymizes email addresses, domain names, notification settings

### STEP 9: Verification and Reporting

- **File:** `09_Verification_and_Reporting.sql`
- **Purpose:** Validates anonymization results, generates compliance reports

### STEP 10: Data Masking Verification

- **File:** `10_Data_Masking_Verification.sql`
- **Purpose:** Performs final verification of data masking coverage

### STEP 11: Performance Optimization

- **File:** `11_Performance_Optimization.sql`
- **Purpose:** Rebuilds indexes, updates statistics, optimizes database performance

### STEP 12: Final Verification

- **File:** `12_Final_Verification.sql`
- **Purpose:** Final verification and reporting

## ADDITIONAL UTILITY FILES

### Performance Enhancement (Optional)
- **CONFIG_MANAGEMENT.sql** - Performance-optimized configuration system with strongly-typed columns, modern accessor functions, and centralized constants. Provides significant performance benefits over the default configuration structure created by Module 01.

### Advanced Verification (Optional)  
- **VERIFICATION_UTILITY.sql** - Advanced verification tools for regulatory audits, security assessments, and specialized compliance reporting. Only needed for advanced compliance scenarios beyond standard verification provided by Modules 10 and 12.

## EXECUTION RECOMMENDATIONS

### Minimum Required Execution (Complete Anonymization)
```sql
:r "01_Setup_and_Validation.sql"
:r "02_Camera_Data_Anonymization.sql"
:r "03_Server_Data_Anonymization.sql"
:r "04_User_Account_Anonymization.sql"
:r "05_Network_Device_Anonymization.sql" 
:r "06_Integration_Credentials_Anonymization.sql"
:r "07_URL_and_FilePath_Anonymization.sql"
:r "08_Email_and_Domain_Anonymization.sql"
:r "09_Verification_and_Reporting.sql"
:r "10_Data_Masking_Verification.sql"
:r "11_Performance_Optimization.sql"
:r "12_Final_Verification.sql"
```

### Recommended Execution (With Performance Optimization)
```sql
:r "01_Setup_and_Validation.sql"
:r "CONFIG_MANAGEMENT.sql"  -- Optional: Performance upgrade
:r "02_Camera_Data_Anonymization.sql"
:r "03_Server_Data_Anonymization.sql"
:r "04_User_Account_Anonymization.sql"
:r "05_Network_Device_Anonymization.sql" 
:r "06_Integration_Credentials_Anonymization.sql"
:r "07_URL_and_FilePath_Anonymization.sql"
:r "08_Email_and_Domain_Anonymization.sql"
:r "09_Verification_and_Reporting.sql"
:r "10_Data_Masking_Verification.sql"
:r "11_Performance_Optimization.sql"
:r "12_Final_Verification.sql"
```

### Advanced Compliance Execution (Full Suite)
```sql
:r "01_Setup_and_Validation.sql"
:r "CONFIG_MANAGEMENT.sql"  -- Optional: Performance upgrade
:r "02_Camera_Data_Anonymization.sql"
:r "03_Server_Data_Anonymization.sql"
:r "04_User_Account_Anonymization.sql"
:r "05_Network_Device_Anonymization.sql" 
:r "06_Integration_Credentials_Anonymization.sql"
:r "07_URL_and_FilePath_Anonymization.sql"
:r "08_Email_and_Domain_Anonymization.sql"
:r "09_Verification_and_Reporting.sql"
:r "10_Data_Masking_Verification.sql"
:r "11_Performance_Optimization.sql"
:r "12_Final_Verification.sql"
:r "VERIFICATION_UTILITY.sql"  -- Optional: Advanced compliance tools
```

## EXECUTION INSTRUCTIONS

1. **Open SQL Server Management Studio (SSMS)**
2. **Connect to your CompleteView database**
3. **Execute modules in numerical order (01-12)**
4. **Monitor execution progress** in Messages tab
5. **Check final verification results** from Module 12

### Batch Execution (Alternative Method)

Create a master script with the commands above, or execute each file individually through SSMS.

## CONFIGURATION

- Configuration managed through `AnonymizationConfig` table (created in Module 01)
- Use `CONFIG_MANAGEMENT.sql` for performance-optimized configuration structure
- All modules use centralized configuration via `dbo.fn_GetConfigValue()` function

## TROUBLESHOOTING

### Common Issues

- **"Table does not exist"**: Ensure Module 01 ran properly
- **"Permission denied"**: Requires db_owner role or equivalent permissions
- **"Verification failed"**: Run Module 12 for detailed analysis, re-run failed modules
- **"Timeout errors"**: Increase SSMS timeout (Tools > Options > Query Execution)

### Verification Results

- **PASSED**: All checks passed, database ready for use
- **PASSED_WITH_WARNINGS**: Minor issues detected, review detailed findings
- **FAILED_MINOR**: Some data missed, re-run specific modules
- **FAILED_MAJOR**: Significant issues detected, investigate thoroughly

## File Descriptions

| File | Purpose | Estimated Time |
|------|---------|----------------|
| `01_Setup_and_Validation.sql` | Database validation and logging setup | 1-2 minutes |
| `02_Camera_Data_Anonymization.sql` | Camera names, locations, ONVIF URLs | 2-5 minutes |
| `03_Server_Data_Anonymization.sql` | Server names, IPs, network config | 2-5 minutes |
| `04_User_Account_Anonymization.sql` | User accounts, passwords, emails | 1-3 minutes |
| `05_Network_Device_Anonymization.sql` | Device IPs, MACs, identifiers | 3-8 minutes |
| `06_Integration_Credentials_Anonymization.sql` | API keys, tokens, credentials | 1-2 minutes |
| `07_URL_and_FilePath_Anonymization.sql` | File paths, URLs, UNC paths | 2-4 minutes |
| `08_Email_and_Domain_Anonymization.sql` | Email addresses, domain names | 1-3 minutes |
| `09_Verification_and_Reporting.sql` | Anonymization validation | 2-3 minutes |
| `10_Data_Masking_Verification.sql` | Pattern detection, security scan | 3-5 minutes |
| `11_Performance_Optimization.sql` | Index rebuild, statistics update | 5-15 minutes |
| `12_Final_Verification.sql` | Final verification | 2-5 minutes |
| `CONFIG_MANAGEMENT.sql` | Performance-optimized configuration system | 2-3 minutes |
| `VERIFICATION_UTILITY.sql` | Advanced verification and compliance tools | 2-3 minutes |

## PERFORMANCE OPTIMIZATIONS

The anonymization system has been optimized for performance:

- **Cursor Elimination**: Replaced row-by-row processing with set-based operations
- **Configuration Optimization**: Optional strongly-typed configuration system available
- **Centralized Constants**: All magic values moved to centralized configuration
- **Index Optimization**: Comprehensive index rebuilding in Module 11

For technical implementation details, see `PERFORMANCE-NOTES.md`.

## REQUIREMENTS

- **SQL Server 2016+** with db_owner permissions
- **SQL Server Management Studio 17.0+**
- **Adequate disk space** (2x database size recommended)

## NOTES

- **Designed for database backups** - not live production systems
- **All anonymization occurs locally** - no external data transmission
- **Full audit trail** maintained in AnonymizationLog table
- **Each file can be executed independently** if needed
