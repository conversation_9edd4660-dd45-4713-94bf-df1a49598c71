-- =====================================================================================
-- MODULE 01: SETUP AND VALIDATION - COMPLETE FOUNDATION
-- =====================================================================================
-- This module creates the complete infrastructure for database anonymization
-- Must be executed FIRST before any other modules
-- =====================================================================================

SET NOCOUNT ON;
SET XACT_ABORT ON;

-- =====================================================================================
-- CONFIGURATION VARIABLES
-- =====================================================================================
DECLARE @DryRun BIT = 0;                    -- Set to 1 for preview mode
DECLARE @CommitChanges BIT = 1;             -- Set to 1 to commit changes
DECLARE @AnonymizationSeed INT = 42;        -- Seed for consistent randomization
DECLARE @BatchSize INT = 1000;              -- Batch processing size
DECLARE @RequireRecentBackup BIT = 0;       -- Require recent backup validation

PRINT '=====================================================================================';
PRINT 'MODULE 01: SETUP AND VALIDATION';
PRINT '=====================================================================================';
PRINT 'Initializing anonymization infrastructure...';
PRINT 'DryRun Mode: ' + CASE WHEN @DryRun = 1 THEN 'ENABLED (Preview Only)' ELSE 'DISABLED (Will Execute)' END;
PRINT 'Commit Changes: ' + CASE WHEN @CommitChanges = 1 THEN 'YES' ELSE 'NO (Will Rollback)' END;
PRINT '';

-- =====================================================================================
-- CREATE CORE CONFIGURATION TABLE
-- =====================================================================================
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationConfig')
BEGIN
    CREATE TABLE dbo.AnonymizationConfig (
        ConfigKey VARCHAR(100) PRIMARY KEY,
        ConfigValue VARCHAR(MAX),
        DataType VARCHAR(20),
        Description VARCHAR(500),
        LastModified DATETIME DEFAULT GETDATE(),
        ModifiedBy VARCHAR(100) DEFAULT SYSTEM_USER
    );
    PRINT 'Created AnonymizationConfig table';
END
ELSE
BEGIN
    PRINT 'AnonymizationConfig table already exists';
END

-- Clear existing configuration to ensure fresh setup
DELETE FROM dbo.AnonymizationConfig;

-- Insert complete default configuration values
INSERT INTO dbo.AnonymizationConfig (ConfigKey, ConfigValue, DataType, Description) VALUES
    -- Core Anonymization Settings
    ('AnonymizationSeed', '42', 'INT', 'Randomization seed for consistent results across all modules'),
    ('AnonymizedDomain', 'company.local', 'NVARCHAR(100)', 'Domain to use for anonymized data'),
    ('EmailSuffix', '@company.local', 'NVARCHAR(100)', 'Email suffix for anonymized emails'),
    ('DefaultPassword', '[ACCOUNT_DISABLED]', 'NVARCHAR(100)', 'Password replacement strategy - disables authentication'),
    
    -- Execution Control
    ('BatchSize', '1000', 'INT', 'Number of records to process in each batch'),
    ('DryRun', '0', 'BIT', 'Set to 1 for preview mode, 0 for actual execution'),
    ('CommitChanges', '1', 'BIT', 'Set to 1 to commit changes, 0 to rollback'),
    ('UsePerformanceOptimizations', '1', 'BIT', 'Enable performance optimizations'),
    ('RequireRecentBackup', '0', 'BIT', 'Require recent backup validation before execution'),
    
    -- IP Range Configuration
    ('CVE_IPRange_Cameras', '10.1.1', 'NVARCHAR(20)', 'IP range for camera devices'),
    ('CVE_IPRange_Servers', '10.2.1', 'NVARCHAR(20)', 'IP range for server devices'),
    ('CVE_IPRange_SessionManager', '172.20.0', 'NVARCHAR(20)', 'IP range for session managers'),
    ('CVE_IPRange_AlarmDevices', '192.168.50', 'NVARCHAR(20)', 'IP range for alarm devices'),
    ('CVE_IPRange_AudioDevices', '192.168.60', 'NVARCHAR(20)', 'IP range for audio devices'),
    
    -- Path Configuration
    ('CVE_FilePath_Prefix', 'C:\AnonymizedData', 'NVARCHAR(100)', 'File path prefix'),
    ('CVE_UNCPath_Prefix', '\\anonymized-server', 'NVARCHAR(100)', 'UNC path prefix'),
    ('CVE_StreamProfile_Prefix', '/stream/device_', 'NVARCHAR(50)', 'Stream profile prefix'),
    
    -- Performance Settings
    ('FragmentationThreshold', '30.0', 'FLOAT', 'Index fragmentation threshold for rebuild'),
    ('StatisticsAgeThreshold', '7', 'INT', 'Days after which statistics are considered stale'),
    ('MaxExecutionTimeMinutes', '120', 'INT', 'Maximum execution time per module in minutes'),
    
    -- Verification Settings
    ('VerificationSampleSize', '100', 'INT', 'Number of records to sample for verification'),
    ('PatternDetectionEnabled', '1', 'BIT', 'Enable pattern-based verification'),
    ('HashVerificationEnabled', '1', 'BIT', 'Enable hash-based verification');

PRINT 'Configuration table populated with ' + CAST(@@ROWCOUNT AS VARCHAR) + ' settings';

-- =====================================================================================
-- CONFIGURATION HELPER FUNCTION
-- =====================================================================================
IF OBJECT_ID('dbo.fn_GetConfigValue') IS NOT NULL
    DROP FUNCTION dbo.fn_GetConfigValue;
GO

CREATE FUNCTION dbo.fn_GetConfigValue(@ConfigKey VARCHAR(100))
RETURNS VARCHAR(MAX)
AS
BEGIN
    DECLARE @Value VARCHAR(MAX);
    
    -- Check if new structure exists (has StringValue column)
    IF COL_LENGTH('dbo.AnonymizationConfig', 'StringValue') IS NOT NULL
    BEGIN
        -- New structure with typed columns
        SELECT @Value = COALESCE(StringValue, CAST(IntValue AS VARCHAR), CAST(BitValue AS VARCHAR), 
                                CAST(DecimalValue AS VARCHAR), CAST(DateValue AS VARCHAR), JsonValue)
        FROM dbo.AnonymizationConfig 
        WHERE ConfigKey = @ConfigKey;
    END
    ELSE
    BEGIN
        -- Legacy structure with ConfigValue column
        SELECT @Value = ConfigValue 
        FROM dbo.AnonymizationConfig 
        WHERE ConfigKey = @ConfigKey;
    END
    
    RETURN ISNULL(@Value, '');
END;
GO

-- =====================================================================================
-- CREATE LOGGING TABLES
-- =====================================================================================

-- Main logging table
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationLog')
BEGIN
    CREATE TABLE dbo.AnonymizationLog (
        LogID INT IDENTITY(1,1) PRIMARY KEY,
        ExecutionID VARCHAR(50),
        ModuleName VARCHAR(100),
        TableName VARCHAR(100),
        ColumnName VARCHAR(100),
        Operation VARCHAR(100),
        RowsAffected INT,
        AnonymizationType VARCHAR(50),
        ExecutionTime DATETIME DEFAULT GETDATE(),
        Notes VARCHAR(MAX),
        Category VARCHAR(50)
    );
    PRINT 'Created AnonymizationLog table';
END

-- Pre-anonymization snapshot table
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationPreSnapshot')
BEGIN
    CREATE TABLE dbo.AnonymizationPreSnapshot (
        SnapshotID INT IDENTITY(1,1) PRIMARY KEY,
        ExecutionID VARCHAR(50),
        TableName VARCHAR(100),
        ColumnName VARCHAR(100),
        RecordKey VARCHAR(200),
        OriginalValue VARCHAR(MAX),
        ValueHash VARCHAR(100),
        CaptureTime DATETIME DEFAULT GETDATE()
    );
    PRINT 'Created AnonymizationPreSnapshot table';
END

-- Post-anonymization snapshot table
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationPostSnapshot')
BEGIN
    CREATE TABLE dbo.AnonymizationPostSnapshot (
        SnapshotID INT IDENTITY(1,1) PRIMARY KEY,
        ExecutionID VARCHAR(50),
        TableName VARCHAR(100),
        ColumnName VARCHAR(100),
        RecordKey VARCHAR(200),
        AnonymizedValue VARCHAR(MAX),
        ValueHash VARCHAR(100),
        CaptureTime DATETIME DEFAULT GETDATE()
    );
    PRINT 'Created AnonymizationPostSnapshot table';
END

-- Create logging buffer table
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'AnonymizationLogBuffer')
BEGIN
    CREATE TABLE dbo.AnonymizationLogBuffer (
        BufferID INT IDENTITY(1,1) PRIMARY KEY,
        ModuleName VARCHAR(100),
        TableName VARCHAR(100),
        ColumnName VARCHAR(100),
        RowsAffected INT,
        OperationType VARCHAR(50),
        StartTime DATETIME DEFAULT GETDATE(),
        EndTime DATETIME,
        Notes VARCHAR(500)
    );
    PRINT 'Created AnonymizationLogBuffer table';
END
ELSE
BEGIN
    -- Clear any existing buffer data from previous runs
    DELETE FROM dbo.AnonymizationLogBuffer;
    PRINT 'Cleared AnonymizationLogBuffer table';
END

-- =====================================================================================
-- CREATE VERIFICATION PROCEDURES
-- =====================================================================================

-- Create snapshot comparison procedure
IF OBJECT_ID('dbo.sp_CompareSnapshotsAndDetectMisses') IS NOT NULL
    DROP PROCEDURE dbo.sp_CompareSnapshotsAndDetectMisses;
GO

CREATE PROCEDURE dbo.sp_CompareSnapshotsAndDetectMisses
    @ExecutionID VARCHAR(50),
    @DryRun BIT = 0
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @MissedCount INT = 0;
    
    -- Compare pre and post snapshots to detect missed anonymization
    SELECT 
        pre.TableName,
        pre.ColumnName,
        pre.RecordKey,
        pre.OriginalValue,
        ISNULL(post.AnonymizedValue, '[NOT_ANONYMIZED]') AS AnonymizedValue
    INTO #MissedRecords
    FROM dbo.AnonymizationPreSnapshot pre
    LEFT JOIN dbo.AnonymizationPostSnapshot post 
        ON pre.ExecutionID = post.ExecutionID 
        AND pre.TableName = post.TableName
        AND pre.ColumnName = post.ColumnName
        AND pre.RecordKey = post.RecordKey
    WHERE pre.ExecutionID = @ExecutionID
    AND (post.AnonymizedValue IS NULL OR pre.OriginalValue = post.AnonymizedValue);
    
    SET @MissedCount = @@ROWCOUNT;
    
    IF @MissedCount > 0
    BEGIN
        PRINT 'WARNING: Found ' + CAST(@MissedCount AS VARCHAR) + ' records that may not have been properly anonymized';
        
        -- Show sample of missed records
        SELECT TOP 10 * FROM #MissedRecords;
        
        -- Log the findings
        INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, Operation, RowsAffected, Notes, ExecutionID)
        VALUES ('Verification', 'MISSED_ANONYMIZATION', 'DETECTION', @MissedCount, 
                'Found records that may not have been properly anonymized', @ExecutionID);
    END
    ELSE
    BEGIN
        PRINT 'GOOD: No missed anonymization detected in snapshot comparison';
    END
    
    DROP TABLE #MissedRecords;
END;
GO

-- Create hash verification procedure
IF OBJECT_ID('dbo.sp_VerifyNoOriginalValuesRemain') IS NOT NULL
    DROP PROCEDURE dbo.sp_VerifyNoOriginalValuesRemain;
GO

CREATE PROCEDURE dbo.sp_VerifyNoOriginalValuesRemain
    @ExecutionID VARCHAR(50),
    @DryRun BIT = 0
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @SurvivorCount INT = 0;
    
    -- Check for original values that survived anonymization
    SELECT 
        pre.TableName,
        pre.ColumnName,
        COUNT(*) AS SurvivorCount
    INTO #Survivors
    FROM dbo.AnonymizationPreSnapshot pre
    INNER JOIN dbo.AnonymizationPostSnapshot post 
        ON pre.ExecutionID = post.ExecutionID 
        AND pre.TableName = post.TableName
        AND pre.ColumnName = post.ColumnName
        AND pre.RecordKey = post.RecordKey
        AND pre.ValueHash = post.ValueHash  -- Same hash = same value
    WHERE pre.ExecutionID = @ExecutionID
    GROUP BY pre.TableName, pre.ColumnName;
    
    SELECT @SurvivorCount = ISNULL(SUM(SurvivorCount), 0) FROM #Survivors;
    
    IF @SurvivorCount > 0
    BEGIN
        PRINT 'WARNING: Found ' + CAST(@SurvivorCount AS VARCHAR) + ' original values that survived anonymization';
        SELECT * FROM #Survivors;
        
        INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, Operation, RowsAffected, Notes, ExecutionID)
        VALUES ('Verification', 'SURVIVOR_DETECTION', 'HASH_VERIFICATION', @SurvivorCount, 
                'Found original values that survived anonymization', @ExecutionID);
    END
    ELSE
    BEGIN
        PRINT 'EXCELLENT: No original values detected in anonymized data';
    END
    
    DROP TABLE #Survivors;
END;
GO

-- Create buffer flush procedure
IF OBJECT_ID('dbo.sp_FlushAnonymizationBuffer') IS NOT NULL
    DROP PROCEDURE dbo.sp_FlushAnonymizationBuffer;
GO

CREATE PROCEDURE dbo.sp_FlushAnonymizationBuffer
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Aggregate and flush buffer to main log
    INSERT INTO dbo.AnonymizationLog (ModuleName, TableName, ColumnName, RowsAffected, Operation, ExecutionTime, Notes)
    SELECT 
        ModuleName,
        TableName,
        ColumnName,
        SUM(RowsAffected) as TotalRowsAffected,
        OperationType,
        MAX(EndTime) as ExecutionTime,
        'Aggregated from buffer: ' + STRING_AGG(Notes, '; ') as AggregatedNotes
    FROM dbo.AnonymizationLogBuffer
    WHERE ModuleName IS NOT NULL
    GROUP BY ModuleName, TableName, ColumnName, OperationType;
    
    -- Clear the buffer after flushing
    DELETE FROM dbo.AnonymizationLogBuffer;
    
    PRINT 'Flushed aggregated logging buffer to AnonymizationLog';
END;
GO

PRINT 'Created essential stored procedures for verification and logging';

-- =====================================================================================
-- DATABASE VALIDATION
-- =====================================================================================
PRINT 'Performing database validation...';

-- Check if this is a CompleteView database
IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name IN ('Camera', 'User', 'Server', 'VideoDevices'))
BEGIN
    PRINT 'WARNING: This does not appear to be a CompleteView database';
    PRINT 'Expected tables (Camera, User, Server, VideoDevices) not found';
END
ELSE
BEGIN
    PRINT 'CompleteView database structure detected';
END

-- Check database size and free space
DECLARE @DatabaseSize DECIMAL(10,2);
DECLARE @FreeSpace DECIMAL(10,2);

SELECT 
    @DatabaseSize = SUM(CAST(size AS DECIMAL(10,2)) * 8 / 1024),
    @FreeSpace = SUM(CASE WHEN type = 0 THEN CAST(size - FILEPROPERTY(name, 'SpaceUsed') AS DECIMAL(10,2)) * 8 / 1024 ELSE 0 END)
FROM sys.database_files;

PRINT 'Database size: ' + CAST(@DatabaseSize AS VARCHAR) + ' MB';
PRINT 'Free space: ' + CAST(@FreeSpace AS VARCHAR) + ' MB';

-- Backup validation (if required)
IF @RequireRecentBackup = 1
BEGIN
    DECLARE @LastBackup DATETIME;
    SELECT @LastBackup = MAX(backup_finish_date)
    FROM msdb.dbo.backupset 
    WHERE database_name = DB_NAME() AND type = 'D';
    
    IF @LastBackup IS NULL OR DATEDIFF(day, @LastBackup, GETDATE()) > 7
    BEGIN
        THROW 50001, 'Recent backup required but not found. Please create a backup before proceeding.', 1;
    END
    ELSE
    BEGIN
        PRINT 'Recent backup found: ' + CONVERT(VARCHAR, @LastBackup, 120);
    END
END

PRINT '';
PRINT 'Setup and validation completed successfully';
PRINT 'Infrastructure ready for anonymization modules';
PRINT '';
