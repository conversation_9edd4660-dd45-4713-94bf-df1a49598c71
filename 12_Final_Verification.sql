-- =====================================================================================
-- METHOD 2: PATTERN-BASED VERIFICATION (STATISTICAL ANALYSIS)
-- =====================================================================================
PRINT '';
PRINT '2. PATTERN-BASED VERIFICATION';
PRINT '=======================================================================';

-- Create verification results table
CREATE TABLE #VerificationStats (
    TableName VARCHAR(100),
    ColumnName VARCHAR(100),
    TotalRecords INT,
    AnonymizedPatterns INT,
    SuspiciousPatterns INT,
    VerificationStatus VARCHAR(20),
    Notes VARCHAR(500)
);

-- Check key tables for anonymization patterns
DECLARE @Tables TABLE (TableName VARCHAR(100), ColumnName VARCHAR(100), ExpectedPattern VARCHAR(200));
INSERT INTO @Tables VALUES 
    ('Camera', 'CameraName', 'Camera_###'),
    ('User', 'Username', 'User######'),
    ('User', 'Email', '%@company.<NAME_EMAIL>'),
    ('User', 'Password', '[REDACTED]'),
    ('Server', 'DisplayName', 'Server###'),
    ('Server', 'IP', '10.%.%.%'),
    ('VideoDevices', 'IpAddressV4', '10.%.%.%'),
    ('UserSession', 'ClientHostname', 'anonymized-client.example.com');

-- Set-based approach: Generate dynamic SQL for all tables at once
DECLARE @VerificationSQL NVARCHAR(MAX) = '';

SELECT @VerificationSQL = @VerificationSQL + 
    'IF EXISTS (SELECT 1 FROM sys.tables WHERE name = ''' + TableName + ''')
     BEGIN
         INSERT INTO #VerificationStats
         SELECT 
             ''' + TableName + ''' as TableName,
             ''' + ColumnName + ''' as ColumnName,
             COUNT(*) as TotalRecords,
             SUM(CASE 
                 WHEN [' + ColumnName + '] LIKE ''Camera[_]%'' OR
                      [' + ColumnName + '] LIKE ''User%'' OR
                      [' + ColumnName + '] LIKE ''Server%'' OR
                      [' + ColumnName + '] LIKE ''%@company.local'' OR
                      [' + ColumnName + '] LIKE ''%@example.com'' OR
                      [' + ColumnName + '] LIKE ''10.%.%.%'' OR
                      [' + ColumnName + '] LIKE ''172.%.%.%'' OR
                      [' + ColumnName + '] = ''[REDACTED]'' OR
                      [' + ColumnName + '] = ''anonymized-client.example.com''
                 THEN 1 ELSE 0 END) as AnonymizedPatterns,
             SUM(CASE 
                 WHEN [' + ColumnName + '] IS NOT NULL AND
                      [' + ColumnName + '] NOT LIKE ''Camera[_]%'' AND
                      [' + ColumnName + '] NOT LIKE ''User%'' AND
                      [' + ColumnName + '] NOT LIKE ''Server%'' AND
                      [' + ColumnName + '] NOT LIKE ''%@company.local'' AND
                      [' + ColumnName + '] NOT LIKE ''%@example.com'' AND
                      [' + ColumnName + '] NOT LIKE ''10.%.%.%'' AND
                      [' + ColumnName + '] NOT LIKE ''172.%.%.%'' AND
                      [' + ColumnName + '] != ''[REDACTED]'' AND
                      [' + ColumnName + '] != ''anonymized-client.example.com''
                 THEN 1 ELSE 0 END) as SuspiciousPatterns,
             CASE 
                 WHEN COUNT(*) = 0 THEN ''EMPTY''
                 WHEN SUM(CASE 
                     WHEN [' + ColumnName + '] IS NOT NULL AND
                          [' + ColumnName + '] NOT LIKE ''Camera[_]%'' AND
                          [' + ColumnName + '] NOT LIKE ''User%'' AND
                          [' + ColumnName + '] NOT LIKE ''Server%'' AND
                          [' + ColumnName + '] NOT LIKE ''%@company.local'' AND
                          [' + ColumnName + '] NOT LIKE ''%@example.com'' AND
                          [' + ColumnName + '] NOT LIKE ''10.%.%.%'' AND
                          [' + ColumnName + '] NOT LIKE ''172.%.%.%'' AND
                          [' + ColumnName + '] != ''[REDACTED]'' AND
                          [' + ColumnName + '] != ''anonymized-client.example.com''
                     THEN 1 ELSE 0 END) = 0 THEN ''PASSED''
                 WHEN SUM(CASE 
                     WHEN [' + ColumnName + '] IS NOT NULL AND
                          [' + ColumnName + '] NOT LIKE ''Camera[_]%'' AND
                          [' + ColumnName + '] NOT LIKE ''User%'' AND
                          [' + ColumnName + '] NOT LIKE ''Server%'' AND
                          [' + ColumnName + '] NOT LIKE ''%@company.local'' AND
                          [' + ColumnName + '] NOT LIKE ''%@example.com'' AND
                          [' + ColumnName + '] NOT LIKE ''10.%.%.%'' AND
                          [' + ColumnName + '] NOT LIKE ''172.%.%.%'' AND
                          [' + ColumnName + '] != ''[REDACTED]'' AND
                          [' + ColumnName + '] != ''anonymized-client.example.com''
                     THEN 1 ELSE 0 END) <= (COUNT(*) * 0.05) THEN ''WARNING''
                 ELSE ''FAILED''
             END as VerificationStatus,
             ''Expected: ' + ExpectedPattern + ''' as Notes
         FROM dbo.[' + TableName + ']
         WHERE [' + ColumnName + '] IS NOT NULL;
     END
     '
FROM @Tables;

-- Execute verification SQL
IF LEN(@VerificationSQL) > 0
    EXEC sp_executesql @VerificationSQL;

-- Display verification results
PRINT 'Pattern-based verification results:';
SELECT 
    TableName + '.' + ColumnName AS [Table.Column],
    TotalRecords AS [Total Records],
    AnonymizedPatterns AS [Anonymized],
    SuspiciousPatterns AS [Suspicious],
    VerificationStatus AS [Status],
    Notes AS [Expected Pattern]
FROM #VerificationStats
ORDER BY 
    CASE VerificationStatus 
        WHEN 'FAILED' THEN 1 
        WHEN 'WARNING' THEN 2 
        WHEN 'PASSED' THEN 3 
        ELSE 4 
    END,
    SuspiciousPatterns DESC;

-- Calculate verification statistics
DECLARE @GoodChecks INT = (SELECT COUNT(*) FROM #VerificationStats WHERE VerificationStatus = 'PASSED');
DECLARE @WarningChecks INT = (SELECT COUNT(*) FROM #VerificationStats WHERE VerificationStatus = 'WARNING');
DECLARE @FailedChecks INT = (SELECT COUNT(*) FROM #VerificationStats WHERE VerificationStatus = 'FAILED');

PRINT '';
PRINT 'Pattern verification summary:';
PRINT '  PASSED: ' + CAST(@GoodChecks AS VARCHAR) + ' checks';
PRINT '  WARNING: ' + CAST(@WarningChecks AS VARCHAR) + ' checks (≤5% suspicious patterns)';
PRINT '  FAILED: ' + CAST(@FailedChecks AS VARCHAR) + ' checks (>5% suspicious patterns)';

-- =====================================================================================
-- METHOD 3: DATA LEAKAGE DETECTION (HIGH-RISK PATTERNS)
-- =====================================================================================
PRINT '';
PRINT '3. DATA LEAKAGE DETECTION';
PRINT '=======================================================================';

-- Create table for additional security checks
CREATE TABLE #AdditionalChecks (
    CheckName VARCHAR(200),
    TableName VARCHAR(100),
    ColumnName VARCHAR(100),
    IssueCount INT,
    SampleValues NVARCHAR(MAX),
    RiskLevel VARCHAR(20)
);

-- Check for potential email addresses that weren't anonymized
IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'User')
BEGIN
    INSERT INTO #AdditionalChecks
    SELECT 
        'Potential Real Email Addresses',
        'User',
        'Email',
        COUNT(*),
        STRING_AGG(CAST(Email AS NVARCHAR(MAX)), ', ') WITHIN GROUP (ORDER BY Email),
        'HIGH'
    FROM dbo.[User]
    WHERE Email IS NOT NULL
    AND Email NOT LIKE '%@company.local'
    AND Email NOT LIKE '%@example.com'
    AND Email LIKE '%@%.%'
    AND Email NOT LIKE 'User%@%'
    HAVING COUNT(*) > 0;
END

-- Check for potential real IP addresses
DECLARE @IPCheckTables TABLE (TableName VARCHAR(100), ColumnName VARCHAR(100));
INSERT INTO @IPCheckTables VALUES 
    ('Camera', 'IpAddress'),
    ('Server', 'IP'),
    ('VideoDevices', 'IpAddressV4'),
    ('NetworkDevice', 'IPAddress');

DECLARE @IPCheckSQL NVARCHAR(MAX) = '';
SELECT @IPCheckSQL = @IPCheckSQL + 
    'IF EXISTS (SELECT 1 FROM sys.tables WHERE name = ''' + TableName + ''')
     BEGIN
         INSERT INTO #AdditionalChecks
         SELECT 
             ''Potential Real IP Addresses'',
             ''' + TableName + ''',
             ''' + ColumnName + ''',
             COUNT(*),
             STRING_AGG([' + ColumnName + '], '', ''),
             ''HIGH''
         FROM dbo.[' + TableName + ']
         WHERE [' + ColumnName + '] IS NOT NULL
         AND [' + ColumnName + '] NOT LIKE ''10.%.%.%''
         AND [' + ColumnName + '] NOT LIKE ''172.%.%.%''
         AND [' + ColumnName + '] NOT LIKE ''192.168.%.%''
         AND [' + ColumnName + '] LIKE ''%.%.%.%''
         AND TRY_CAST([' + ColumnName + '] AS INET_ATON) IS NOT NULL
         HAVING COUNT(*) > 0;
     END
     '
FROM @IPCheckTables;

IF LEN(@IPCheckSQL) > 0
    EXEC sp_executesql @IPCheckSQL;

-- Check for potential real hostnames/server names
IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'Server')
BEGIN
    INSERT INTO #AdditionalChecks
    SELECT 
        'Potential Real Server Names',
        'Server',
        'DisplayName',
        COUNT(*),
        STRING_AGG(DisplayName, ', ') WITHIN GROUP (ORDER BY DisplayName),
        'MEDIUM'
    FROM dbo.[Server]
    WHERE DisplayName IS NOT NULL
    AND DisplayName NOT LIKE 'Server%'
    AND DisplayName NOT LIKE 'anonymized%'
    AND LEN(DisplayName) > 3
    HAVING COUNT(*) > 0;
END

-- Check for potential real camera names
IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'Camera')
BEGIN
    INSERT INTO #AdditionalChecks
    SELECT 
        'Potential Real Camera Names',
        'Camera',
        'CameraName',
        COUNT(*),
        STRING_AGG(CameraName, ', ') WITHIN GROUP (ORDER BY CameraName),
        'MEDIUM'
    FROM dbo.[Camera]
    WHERE CameraName IS NOT NULL
    AND CameraName NOT LIKE 'Camera_%'
    AND CameraName NOT LIKE 'anonymized%'
    AND LEN(CameraName) > 3
    HAVING COUNT(*) > 0;
END

-- Display data leakage detection results
DECLARE @DataLeakageCount INT = (SELECT COUNT(*) FROM #AdditionalChecks WHERE RiskLevel = 'HIGH');

PRINT 'Data leakage detection results:';
IF EXISTS (SELECT 1 FROM #AdditionalChecks)
BEGIN
    SELECT 
        CheckName AS [Security Check],
        TableName + '.' + ColumnName AS [Location],
        IssueCount AS [Issues Found],
        RiskLevel AS [Risk Level],
        LEFT(SampleValues, 100) + CASE WHEN LEN(SampleValues) > 100 THEN '...' ELSE '' END AS [Sample Values]
    FROM #AdditionalChecks
    ORDER BY 
        CASE RiskLevel WHEN 'HIGH' THEN 1 WHEN 'MEDIUM' THEN 2 ELSE 3 END,
        IssueCount DESC;
END
ELSE
BEGIN
    PRINT 'No data leakage patterns detected.';
END

PRINT '';
PRINT 'High-risk data leakage issues: ' + CAST(@DataLeakageCount AS VARCHAR);

-- Show detailed suspicious values if requested
IF @DetailedReports = 1 AND EXISTS (SELECT 1 FROM #VerificationStats WHERE VerificationStatus IN ('FAILED', 'WARNING'))
BEGIN
    PRINT '';
    PRINT 'DETAILED SUSPICIOUS VALUE ANALYSIS:';
    PRINT '=======================================================================';
    
    DECLARE @SuspiciousSQL NVARCHAR(MAX) = '';
    
    SELECT @SuspiciousSQL = @SuspiciousSQL + 
        'PRINT '''';
         PRINT ''Analyzing suspicious values in ' + TableName + '.' + ColumnName + ':'';
         PRINT ''----------------------------------------'';
         SELECT TOP ' + CAST(@MaxSampleRecords AS VARCHAR) + ' [' + ColumnName + '] as SuspiciousValue, COUNT(*) as Occurrences
         FROM dbo.[' + TableName + '] 
         WHERE [' + ColumnName + '] IS NOT NULL 
           AND [' + ColumnName + '] NOT LIKE ''Camera[_]%''
           AND [' + ColumnName + '] NOT LIKE ''User%''
           AND [' + ColumnName + '] NOT LIKE ''Server%''
           AND [' + ColumnName + '] NOT LIKE ''%@company.local''
           AND [' + ColumnName + '] NOT LIKE ''%@example.com''
           AND [' + ColumnName + '] NOT LIKE ''10.%.%.%''
           AND [' + ColumnName + '] NOT LIKE ''172.%.%.%''
           AND [' + ColumnName + '] != ''[REDACTED]''
           AND [' + ColumnName + '] != ''anonymized-client.example.com''
         GROUP BY [' + ColumnName + ']
         ORDER BY COUNT(*) DESC;
         '
    FROM #VerificationStats 
    WHERE VerificationStatus IN ('FAILED', 'WARNING') AND TotalRecords > 0;
    
    -- Execute all suspicious value queries
    IF LEN(@SuspiciousSQL) > 0
        EXEC sp_executesql @SuspiciousSQL;
    ELSE
        PRINT 'No suspicious patterns detected in failed/warning checks.';
END